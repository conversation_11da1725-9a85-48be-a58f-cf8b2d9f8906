namespace ThuneeAPI.Models
{
    public class ApiResponse<T>
    {
        public bool Success { get; set; }
        public T? Data { get; set; }
        public string? Error { get; set; }
        public string? Message { get; set; }

        public static ApiResponse<T> SuccessResult(T data, string? message = null)
        {
            return new ApiResponse<T>
            {
                Success = true,
                Data = data,
                Message = message
            };
        }

        public static ApiResponse<T> ErrorResult(string error)
        {
            return new ApiResponse<T>
            {
                Success = false,
                Error = error
            };
        }
    }

    public class CreateLobbyResponse
    {
        public string LobbyCode { get; set; } = string.Empty;
        public string PartnerInviteCode { get; set; } = string.Empty;
        public string OpponentInviteCode { get; set; } = string.Empty;
    }

    public class JoinLobbyResponse
    {
        public string? ActualLobbyCode { get; set; }
        public bool IsInviteCode { get; set; }
    }

    public class PlayersUpdatedResponse
    {
        public List<Player> Players { get; set; } = new();
        public Dictionary<int, List<Player>> Teams { get; set; } = new();
        public Dictionary<int, bool> TeamReady { get; set; } = new();
    }

    public class TeamNamesUpdatedResponse
    {
        public Dictionary<int, string> TeamNames { get; set; } = new();
    }

    public class TeamReadyUpdatedResponse
    {
        public Dictionary<int, bool> TeamReady { get; set; } = new();
        public List<Player> Players { get; set; } = new();
    }

    public class GameStartedResponse
    {
        public List<Player> Players { get; set; } = new();
        public Dictionary<int, List<Player>> Teams { get; set; } = new();
        public Dictionary<int, string>? TeamNames { get; set; }
    }

    public class GamePhaseUpdatedResponse
    {
        public string Phase { get; set; } = string.Empty;
        public List<Player> Players { get; set; } = new();
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class MatchFoundResponse
    {
        public List<Player> Players { get; set; } = new();
        public Dictionary<int, List<Player>> Teams { get; set; } = new();
        public Dictionary<int, string> TeamNames { get; set; } = new();
        public string? GameLobbyCode { get; set; }
    }

    public class MatchStatusUpdateResponse
    {
        public bool IsFindingMatch { get; set; }
        public string? MatchedLobby { get; set; }
        public List<Player>? MatchedTeam { get; set; }
        public string? MatchedTeamName { get; set; }
        public string? GameLobbyCode { get; set; }
    }

    public class TimeframeOptionsResponse
    {
        public List<int> TimeOptions { get; set; } = new();
        public int VotingTimeLimit { get; set; }
    }

    public class TimeframeVoteResponse
    {
        public Dictionary<string, int> Votes { get; set; } = new();
        public Dictionary<int, int> VoteCounts { get; set; } = new();
        public bool AllPlayersVoted { get; set; }
        public int? SelectedTimeframe { get; set; }
    }

    public class CardDealtResponse
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int PlayerIndex { get; set; }
        public Card Card { get; set; } = new();
        public bool IsDealer { get; set; }
        public int CardsDealtToPlayer { get; set; }
        public int TotalCardsDealt { get; set; }
    }

    public class DealerFoundResponse
    {
        public string DealerId { get; set; } = string.Empty;
        public string DealerName { get; set; } = string.Empty;
        public int DealerTeam { get; set; }
        public string TrumpSelectorId { get; set; } = string.Empty;
        public string TrumpSelectorName { get; set; } = string.Empty;
        public int TrumpSelectorTeam { get; set; }
        public Card DealerCard { get; set; } = new();
        public List<Player> Players { get; set; } = new();
    }

    public class DealerDeterminationStartedResponse
    {
        public List<Player> Players { get; set; } = new();
        public int CurrentPlayerIndex { get; set; }
    }

    public class DealingCardToResponse
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int PlayerIndex { get; set; }
    }

    public class DealerDeterminationAbortedResponse
    {
        public string Reason { get; set; } = string.Empty;
    }

    public class CardsDealtResponse
    {
        public Dictionary<string, List<Card>> PlayerCards { get; set; } = new();
        public int CardsPerPlayer { get; set; }
        public bool DealingComplete { get; set; }
    }

    public class TrumpSelectedResponse
    {
        public string TrumpSuit { get; set; } = string.Empty;
        public string TrumpSelectorId { get; set; } = string.Empty;
        public string TrumpSelectorName { get; set; } = string.Empty;
    }

    public class PlayerTurnResponse
    {
        public string PlayerId { get; set; } = string.Empty;
        public bool IsFirstPlayerAfterThunee { get; set; }
    }

    public class CardPlayedResponse
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public Card Card { get; set; } = new();
        public int HandId { get; set; }
        public List<Card> HandCards { get; set; } = new();
    }

    public class HandCompletedResponse
    {
        public int HandId { get; set; }
        public string WinnerId { get; set; } = string.Empty;
        public string WinnerName { get; set; } = string.Empty;
        public int WinnerTeam { get; set; }
        public List<Card> HandCards { get; set; } = new();
        public int Points { get; set; }
        public string? NextPlayerId { get; set; }
    }

    public class BallCompletedResponse
    {
        public int BallId { get; set; }
        public int Winner { get; set; }
        public Dictionary<string, int> Points { get; set; } = new();
        public string? NextDealer { get; set; }
        public Dictionary<string, int> BallScores { get; set; } = new();
        public bool FourBallAwarded { get; set; }
        public string? FourBallOption { get; set; }
        public int? FourBallWinningTeam { get; set; }
        public int BallsAwarded { get; set; }
    }

    public class GameEndedResponse
    {
        public int WinningTeam { get; set; }
        public Dictionary<string, int> FinalBallScores { get; set; } = new();
        public List<BallHistory> GameHistory { get; set; } = new();
        public string Reason { get; set; } = string.Empty;
    }

    public class JordhiCalledResponse
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int Value { get; set; }
        public string JordhiSuit { get; set; } = string.Empty;
        public bool IsValid { get; set; }
    }

    public class JordhiCardsRevealedResponse
    {
        public string PlayerName { get; set; } = string.Empty;
        public int PlayerTeam { get; set; }
        public int Value { get; set; }
        public string JordhiSuit { get; set; } = string.Empty;
        public bool CardsRevealed { get; set; }
        public List<Card>? JordhiCards { get; set; }
    }

    public class FourBallResultResponse
    {
        public string BallType { get; set; } = string.Empty;
        public string Option { get; set; } = string.Empty;
        public string TargetPlayer { get; set; } = string.Empty;
        public string TargetPlayerName { get; set; } = string.Empty;
        public int TargetTeam { get; set; }
        public string AccuserId { get; set; } = string.Empty;
        public string AccuserName { get; set; } = string.Empty;
        public int AccuserTeam { get; set; }
        public int HandNumber { get; set; }
        public bool IsValid { get; set; }
        public int WinningTeam { get; set; }
        public int BallsAwarded { get; set; }
        public Dictionary<string, int> BallScores { get; set; } = new();
        public string Reason { get; set; } = string.Empty;
        public List<Card> SelectedHandCards { get; set; } = new();
    }

    public class AvailableGamesResponse
    {
        public List<AvailableGame> Games { get; set; } = new();
    }

    public class AvailableGame
    {
        public string GameCode { get; set; } = string.Empty;
        public string? PartnerInviteCode { get; set; }
        public string? OpponentInviteCode { get; set; }
        public Dictionary<int, string> TeamNames { get; set; } = new();
        public int PlayerCount { get; set; }
        public int SpectatorCount { get; set; }
    }

    public class SpectatorJoinResponse
    {
        public GameLobby GameLobby { get; set; } = new();
    }

    public class VideoRoomUsersResponse
    {
        public List<VideoUser> Users { get; set; } = new();
    }

    public class VideoUser
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class VideoUserJoinedResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class VideoUserLeftResponse
    {
        public string Id { get; set; } = string.Empty;
        public string? Name { get; set; }
    }

    public class VideoSignalResponse
    {
        public string From { get; set; } = string.Empty;
        public object Signal { get; set; } = new();
        public string? Name { get; set; }
    }

    public class VideoSignalErrorResponse
    {
        public string To { get; set; } = string.Empty;
        public string Error { get; set; } = string.Empty;
    }

    public class ChatMessageResponse
    {
        public string PlayerId { get; set; } = string.Empty;
        public string PlayerName { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
    }
}
