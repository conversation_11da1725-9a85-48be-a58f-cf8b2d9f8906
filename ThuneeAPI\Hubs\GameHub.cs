using Microsoft.AspNetCore.SignalR;
using ThuneeAPI.Models;
using ThuneeAPI.Services;

namespace ThuneeAPI.Hubs
{
    public class GameHub : Hub
    {
        private readonly ILobbyService _lobbyService;
        private readonly IGameService _gameService;
        private readonly ISpectatorService _spectatorService;

        public GameHub(ILobbyService lobbyService, IGameService gameService, ISpectatorService spectatorService)
        {
            _lobbyService = lobbyService;
            _gameService = gameService;
            _spectatorService = spectatorService;

            // Wire up game service events
            _gameService.DealerDeterminationReset += async (lobbyCode) =>
            {
                await Clients.Group(lobbyCode).SendAsync("reset_dealer_determination");
            };

            _gameService.DealerDeterminationStarted += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("dealer_determination_started", response);
            };

            _gameService.DealingCardTo += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("dealing_card_to", response);
            };

            _gameService.CardDealt += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("card_dealt", response);
            };

            _gameService.CardsDealt += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("cards_dealt", response);
            };

            _gameService.DealerFound += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("dealer_found", response);
            };

            _gameService.DealerDeterminationAborted += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("dealer_determination_aborted", response);
            };

            _gameService.TimeframeVoteUpdated += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("TimeframeVoteUpdated", response);
            };

            _gameService.GamePhaseUpdated += async (lobbyCode, response) =>
            {
                await Clients.Group(lobbyCode).SendAsync("GamePhaseUpdated", response);
            };
        }

        public override async Task OnConnectedAsync()
        {
            Console.WriteLine($"User connected: {Context.ConnectionId}");
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            Console.WriteLine($"User disconnected: {Context.ConnectionId}");

            // Clean up lobby connections
            _lobbyService.RemoveConnectionFromLobby(Context.ConnectionId);

            // Clean up spectator connections
            await _spectatorService.RemoveSpectatorAsync(Context.ConnectionId);

            await base.OnDisconnectedAsync(exception);
        }

        // Lobby Management
        public async Task<ApiResponse<CreateLobbyResponse>> CreateLobby(CreateLobbyRequest request)
        {
            try
            {
                var result = await _lobbyService.CreateLobbyAsync(Context.ConnectionId, request.PlayerName, request.TeamName, request.TimeSettings);

                // Join the SignalR group
                await Groups.AddToGroupAsync(Context.ConnectionId, result.LobbyCode);

                return ApiResponse<CreateLobbyResponse>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<CreateLobbyResponse>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<JoinLobbyResponse>> JoinLobby(JoinLobbyRequest request)
        {
            try
            {
                var result = await _lobbyService.JoinLobbyAsync(Context.ConnectionId, request.LobbyCode, request.PlayerName);
                var actualLobbyCode = result.ActualLobbyCode ?? request.LobbyCode;

                // Join the SignalR group
                await Groups.AddToGroupAsync(Context.ConnectionId, actualLobbyCode);

                return ApiResponse<JoinLobbyResponse>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<JoinLobbyResponse>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> SwitchTeam(SetTeamReadyRequest request)
        {
            try
            {
                var success = await _lobbyService.SwitchTeamAsync(Context.ConnectionId, request.LobbyCode);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to switch team");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> RequestTimeframeOptions()
        {
            try
            {
                var lobby = _lobbyService.GetLobbyByConnectionId(Context.ConnectionId);
                if (lobby == null)
                {
                    return ApiResponse<object>.ErrorResult("Lobby not found");
                }

                // Send timeframe options to the requesting player
                var timeOptions = lobby.TimeSettings?.PlayTimeframeOptions ?? new List<int> { 3, 4, 5, 6, 60 };
                var votingTimeLimit = lobby.TimeSettings?.VotingTimeLimit ?? 15;

                await Clients.Caller.SendAsync("TimeframeOptionsUpdated", new
                {
                    timeOptions,
                    votingTimeLimit
                });

                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> UpdateTeamName(UpdateTeamNameRequest request)
        {
            try
            {
                var success = await _lobbyService.UpdateTeamNameAsync(Context.ConnectionId, request.LobbyCode, request.TeamNumber, request.TeamName);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to update team name");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> SetTeamReady(SetTeamReadyRequest request)
        {
            try
            {
                var success = await _lobbyService.SetTeamReadyAsync(Context.ConnectionId, request.LobbyCode, request.Ready);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to set team ready status");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> StartGame(StartGameRequest request)
        {
            try
            {
                var success = await _lobbyService.StartGameAsync(Context.ConnectionId, request.LobbyCode);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to start game");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> FindMatch(FindMatchRequest request)
        {
            try
            {
                var success = await _lobbyService.FindMatchAsync(Context.ConnectionId, request.LobbyCode);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to find match");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> CancelFindMatch(CancelFindMatchRequest request)
        {
            try
            {
                var success = await _lobbyService.CancelFindMatchAsync(Context.ConnectionId, request.LobbyCode);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to cancel find match");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        // Game Actions
        public async Task<ApiResponse<object>> VoteTimeframe(VoteTimeframeRequest request)
        {
            try
            {
                var success = await _gameService.VoteTimeframeAsync(Context.ConnectionId, request.Timeframe);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to vote for timeframe");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> PlayCard(PlayCardRequest request)
        {
            try
            {
                var success = await _gameService.PlayCardAsync(Context.ConnectionId, request.Card);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to play card");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> SelectTrump(SelectTrumpRequest request)
        {
            try
            {
                var success = await _gameService.SelectTrumpAsync(Context.ConnectionId, request.TrumpSuit);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to select trump");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> Bid(BidRequest request)
        {
            try
            {
                var success = await _gameService.BidAsync(Context.ConnectionId, request.BidAmount);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to place bid");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> PassBid(PassBidRequest request)
        {
            try
            {
                var success = await _gameService.PassBidAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to pass bid");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> ShuffleCards(ShuffleCardsRequest request)
        {
            try
            {
                var success = await _gameService.ShuffleCardsAsync(Context.ConnectionId, request.ShuffleType);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to shuffle cards");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> CutCards(CutCardsRequest request)
        {
            try
            {
                var success = await _gameService.CutCardsAsync(Context.ConnectionId, request.CutPosition);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to cut cards");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> DealCards(DealCardsRequest request)
        {
            try
            {
                var success = await _gameService.DealCardsAsync(Context.ConnectionId, request.CardsPerPlayer);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to deal cards");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> StartDealerDetermination(StartDealerDeterminationRequest request)
        {
            try
            {
                var success = await _gameService.StartDealerDeterminationAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to start dealer determination");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> PassThunee()
        {
            try
            {
                var success = await _gameService.PassThuneeAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to pass Thunee");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> HoldGame()
        {
            try
            {
                var success = await _gameService.HoldGameAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to hold game");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> RequestCut(RequestCutRequest request)
        {
            try
            {
                // This would be implemented in the game service
                // For now, return success as a placeholder
                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> SkipCut()
        {
            try
            {
                // This would be implemented in the game service
                // For now, return success as a placeholder
                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> RequestMissingCards()
        {
            try
            {
                // This would be implemented in the game service
                // For now, return success as a placeholder
                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        // Special Calls
        public async Task<ApiResponse<object>> CallJordhi(CallJordhiRequest request)
        {
            try
            {
                var success = await _gameService.CallJordhiAsync(Context.ConnectionId, request.Value, request.JordhiSuit, request.JordhiCards);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to call Jordhi");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> RevealJordhi(RevealJordhiRequest request)
        {
            try
            {
                var success = await _gameService.RevealJordhiAsync(Context.ConnectionId, request.JordhiValue, request.JordhiSuit, request.JordhiCards, request.RevealCards);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to reveal Jordhi");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> CallDouble(CallDoubleRequest request)
        {
            try
            {
                var success = await _gameService.CallDoubleAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to call Double");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> CallKhanak(CallKhanakRequest request)
        {
            try
            {
                var success = await _gameService.CallKhanakAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to call Khanak");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> CallThunee(CallThuneeRequest request)
        {
            try
            {
                var success = await _gameService.CallThuneeAsync(Context.ConnectionId, request.FirstCard);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to call Thunee");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> FourBall(FourBallRequest request)
        {
            try
            {
                var success = await _gameService.FourBallAsync(Context.ConnectionId, request.BallType, request.Option, request.AccusedPlayerId, request.HandNumber);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to call 4-ball");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        // Spectator functionality
        public async Task<ApiResponse<SpectatorJoinResponse>> JoinAsSpectator(JoinSpectatorRequest request)
        {
            try
            {
                var result = await _spectatorService.JoinAsSpectatorAsync(Context.ConnectionId, request.GameCode, request.SpectatorName);

                // Join the SignalR group for the game
                await Groups.AddToGroupAsync(Context.ConnectionId, request.GameCode);

                return ApiResponse<SpectatorJoinResponse>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<SpectatorJoinResponse>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<object>> LeaveSpectatorMode(LeaveSpectatorRequest request)
        {
            try
            {
                var success = await _spectatorService.RemoveSpectatorAsync(Context.ConnectionId);
                if (success)
                {
                    return ApiResponse<object>.SuccessResult(new { });
                }
                return ApiResponse<object>.ErrorResult("Failed to leave spectator mode");
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }

        public async Task<ApiResponse<AvailableGamesResponse>> GetAvailableGames()
        {
            try
            {
                var result = await _spectatorService.GetAvailableGamesAsync();
                return ApiResponse<AvailableGamesResponse>.SuccessResult(result);
            }
            catch (Exception ex)
            {
                return ApiResponse<AvailableGamesResponse>.ErrorResult(ex.Message);
            }
        }

        // Chat functionality
        public async Task<ApiResponse<object>> SendChatMessage(SendChatMessageRequest request)
        {
            try
            {
                var lobbyCode = _lobbyService.GetLobbyCodeByConnectionId(Context.ConnectionId) ?? request.LobbyCode;
                if (string.IsNullOrEmpty(lobbyCode))
                {
                    return ApiResponse<object>.ErrorResult("No lobby found");
                }

                var lobby = _lobbyService.GetLobby(lobbyCode);
                if (lobby == null)
                {
                    return ApiResponse<object>.ErrorResult("Lobby not found");
                }

                var player = lobby.Players.FirstOrDefault(p => p.Id == Context.ConnectionId);
                if (player == null)
                {
                    return ApiResponse<object>.ErrorResult("Player not found");
                }

                var chatMessage = new ChatMessageResponse
                {
                    PlayerId = Context.ConnectionId,
                    PlayerName = player.Name,
                    Message = request.Message,
                    Timestamp = DateTime.UtcNow
                };

                // Send to all players in the lobby
                await Clients.Group(lobbyCode).SendAsync("chat_message", chatMessage);

                return ApiResponse<object>.SuccessResult(new { });
            }
            catch (Exception ex)
            {
                return ApiResponse<object>.ErrorResult(ex.Message);
            }
        }
    }
}
