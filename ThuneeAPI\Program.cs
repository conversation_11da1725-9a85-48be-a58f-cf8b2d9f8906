using Microsoft.AspNetCore.SignalR;
using ThuneeAPI.Hubs;
using ThuneeAPI.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Add SignalR
builder.Services.AddSignalR(options =>
{
    options.EnableDetailedErrors = true;
    options.KeepAliveInterval = TimeSpan.FromSeconds(15);
    options.ClientTimeoutInterval = TimeSpan.FromSeconds(60);
    options.HandshakeTimeout = TimeSpan.FromSeconds(30);
    options.MaximumReceiveMessageSize = 100 * 1024 * 1024; // 100MB
});

// Add CORS
var corsOrigins = builder.Configuration.GetSection("Cors:AllowedOrigins").Get<string[]>() ?? new[] { "*" };

builder.Services.AddCors(options =>
{
    if (builder.Environment.IsDevelopment())
    {
        options.AddPolicy("AllowAll", policy =>
        {
            policy.WithOrigins(corsOrigins)
                  .AllowAnyMethod()
                  .AllowAnyHeader()
                  .AllowCredentials();
        });
    }
    else
    {
        options.AddPolicy("Production", policy =>
        {
            policy.WithOrigins(corsOrigins)
                  .AllowAnyMethod()
                  .AllowAnyHeader()
                  .AllowCredentials();
        });
    }
});

// Register services
builder.Services.AddSingleton<ILobbyService, LobbyService>();
builder.Services.AddSingleton<IGameService, GameService>();
builder.Services.AddSingleton<ISpectatorService, SpectatorService>();
builder.Services.AddSingleton<IVideoService, VideoService>();
builder.Services.AddSingleton<ICardService, CardService>();
builder.Services.AddSingleton<IBallService, BallService>();
builder.Services.AddSingleton<ITurnService, TurnService>();
builder.Services.AddSingleton<ITimeframeService, TimeframeService>();

var app = builder.Build();

// Wire up service events to SignalR
var lobbyService = app.Services.GetRequiredService<ILobbyService>();
var gameService = app.Services.GetRequiredService<IGameService>();
var turnService = app.Services.GetRequiredService<ITurnService>();

// Get the hub context for broadcasting events
var gameHubContext = app.Services.GetRequiredService<IHubContext<ThuneeAPI.Hubs.GameHub>>();

// Wire up lobby service events
lobbyService.PlayersUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("PlayersUpdated", data);
};

lobbyService.TeamNamesUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TeamNamesUpdated", data);
};

lobbyService.TeamReadyUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TeamReadyUpdated", data);
};

lobbyService.GameStarted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("GameStarted", data);
};

lobbyService.GamePhaseUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("GamePhaseUpdated", data);
};

lobbyService.MatchFound += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("MatchFound", data);
};

lobbyService.MatchStatusUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("MatchStatusUpdate", data);
};

// Wire up game service events
gameService.TimeframeOptionsUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TimeframeOptionsUpdated", data);
};

gameService.TimeframeVoteUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TimeframeVoteUpdated", data);
};

gameService.CardDealt += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("CardDealt", data);
};

gameService.DealerFound += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("DealerFound", data);
};

gameService.CardsDealt += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("CardsDealt", data);
};

gameService.TrumpSelected += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TrumpSelected", data);
};

gameService.PlayerTurn += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("PlayerTurn", data);
};

gameService.CardPlayed += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("CardPlayed", data);
};

gameService.HandCompleted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("HandCompleted", data);
};

gameService.BallCompleted += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("BallCompleted", data);
};

gameService.GameEnded += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("GameEnded", data);
};

gameService.JordhiCalled += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("JordhiCalled", data);
};

gameService.JordhiCardsRevealed += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("JordhiCardsRevealed", data);
};

gameService.FourBallResult += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("FourBallResult", data);
};

gameService.GamePhaseUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("GamePhaseUpdated", data);
};

// Wire up turn service events
turnService.PlayerTurnUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("PlayerTurnUpdated", data);
};

turnService.TurnTimerUpdated += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TurnTimerUpdated", data);
};

turnService.TurnTimeout += async (lobbyCode, data) =>
{
    await gameHubContext.Clients.Group(lobbyCode).SendAsync("TurnTimeout", data);
};

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseCors("AllowAll");
}
else
{
    app.UseCors("Production");
}

app.UseHttpsRedirection();
app.UseAuthorization();

app.MapControllers();

// Map SignalR hubs
app.MapHub<GameHub>("/gameHub");
app.MapHub<VideoHub>("/videoHub");

// Add a simple test endpoint
app.MapGet("/", () =>
{
    var port = app.Environment.IsDevelopment() ? "3001" : "96";
    return Results.Content($@"
    <html>
      <head>
        <title>Thunee SignalR Server</title>
        <style>
          body {{ font-family: Arial, sans-serif; background: #222; color: #E1C760; margin: 0; padding: 20px; text-align: center; }}
          .container {{ max-width: 800px; margin: 0 auto; background: #333; padding: 20px; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.3); }}
          h1 {{ color: #E1C760; }}
          .status {{ padding: 10px; background: #444; border-radius: 4px; margin: 20px 0; }}
          .success {{ color: #4CAF50; }}
        </style>
      </head>
      <body>
        <div class='container'>
          <h1>Thunee SignalR Server</h1>
          <div class='status'>
            <p>Server is running on port <strong>{port}</strong></p>
            <p class='success'>✓ SignalR server is active</p>
          </div>
          <p>This server handles SignalR connections for the Thunee card game.</p>
          <p>To play the game, please open the Thunee game client application.</p>
          <p><a href='/debug/match-queue' style='color: #E1C760;'>View Match Queue Debug Info</a></p>
        </div>
      </body>
    </html>", "text/html");
});

app.Run();
