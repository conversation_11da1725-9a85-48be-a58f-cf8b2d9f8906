using ThuneeAPI.Models;

namespace ThuneeAPI.Services
{
    public class TurnService : ITurnService
    {
        private readonly ITimeframeService _timeframeService;

        public TurnService(ITimeframeService timeframeService)
        {
            _timeframeService = timeframeService;
        }

        // Events
        public event Func<string, PlayerTurnResponse, Task>? PlayerTurnUpdated;
        public event Func<string, object, Task>? TurnTimerUpdated;
        public event Func<string, object, Task>? TurnTimeout;

        public async Task SetPlayerTurnAsync(string lobbyCode, string matchedLobbyCode, string playerId, bool isHandComplete = false)
        {
            // Check if this is the first player's turn after Thunee opportunities
            // This would need access to lobby state to determine
            var isFirstPlayerAfterThunee = false; // Placeholder

            // Trigger player turn event
            if (PlayerTurnUpdated != null)
            {
                await PlayerTurnUpdated(lobbyCode, new PlayerTurnResponse
                {
                    PlayerId = playerId,
                    IsFirstPlayerAfterThunee = isFirstPlayerAfterThunee
                });

                // Also send to matched lobby if different
                if (matchedLobbyCode != lobbyCode)
                {
                    await <PERSON>TurnUpdated(matchedLobbyCode, new PlayerTurnResponse
                    {
                        PlayerId = playerId,
                        IsFirstPlayerAfterThunee = isFirstPlayerAfterThunee
                    });
                }
            }

            // If this is the end of a hand, don't start a timer
            if (isHandComplete)
            {
                return;
            }

            // Start turn timer would be implemented here
            // This would involve creating a timer and updating the lobby state
        }

        public void ClearTurnTimer(Lobby lobby)
        {
            // Clear any existing turn timer
            // Implementation would depend on how timers are managed
            if (lobby.TurnTimerState != null)
            {
                lobby.TurnTimerState.TimerActive = false;
            }
        }

        public async Task StartTurnTimerAsync(Lobby lobby, string playerId)
        {
            // Initialize the turn timer
            var turnTimerState = _timeframeService.InitTurnTimer(lobby, playerId);
            var timeframe = turnTimerState.TimeRemaining;

            Console.WriteLine($"Starting turn timer for player {playerId} with {timeframe} seconds");

            // Start the timer logic here
            // This would involve creating a background task or timer that counts down
            // and triggers timeout events when the time expires
            await Task.CompletedTask;
        }

        public async Task UpdateTurnTimerAsync(Lobby lobby)
        {
            await _timeframeService.UpdateTurnTimerAsync(lobby);
        }

        public void StopTurnTimer(Lobby lobby)
        {
            _timeframeService.StopTurnTimer(lobby);
        }
    }
}
